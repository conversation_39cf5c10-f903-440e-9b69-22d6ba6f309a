# PowerShell版本的API Key测试脚本

$API_KEYS = @(
    "KZSV9a-MDw-yvGeExZcjuZ9cV7Qo3HdoCQnAOOdS8xUfyD-1dv2-EiIc0S8MQ0hYZQyKkxOi0hH1s_C0AoIsxA",
    "CHqHzPh3RdLK3B15Aoti5qvx24BErLhiPdfWluVb1ngN1mfy7i1KkqSoQujpVvQzxL4L35nnlKz8JrG38WguPw",
    "IzJtE7D0IyKS3SDcFY6MAG9BUr5mqvDgFW6EWY51ZBOAdQQf21jHcGuUGT7bN3APphM2gb5oV03SMWdhGrd9PA",
    "rW5pZyQF6nrTE3evSFB3zIDMQT1jQL2rhfH-B4bU46tpoH_aM_DMeHvxarAbRZ-YSgI5dCAeWaPRPgIrQxN5zw",
    "WcUjkZP0TGOQFSlUjByWnzxzL9OoztiIbJBhzRrmIHXlXFOVk9eOfnMZLOCymgmgAiOwtmWB33msm0ra_3vQiw",
    "cNFp15XM0U0vuciTLLkA9AzhRrXtP2bAFR5EO5ze7_wrxDpS_Ter1vsKEr-iuXsg9P4tsgfp_4LnsheTIvDj5A"
)

$URL = "https://www.sophnet.com/api/open-apis/chat/completions"

Write-Host "🔑 测试6个API Key..." -ForegroundColor Blue

for ($i = 0; $i -lt $API_KEYS.Count; $i++) {
    $keyNum = $i + 1
    $apiKey = $API_KEYS[$i]
    
    Write-Host "测试Key #${keyNum}: $($apiKey.Substring(0, 20))..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $apiKey"
        "Content-Type" = "application/json"
    }
    
    $body = @{
        model = "DeepSeek-V3-Fast"
        messages = @(
            @{
                role = "user"
                content = "测试"
            }
        )
        max_tokens = 50
        temperature = 0.3
    } | ConvertTo-Json -Depth 3
    
    try {
        $response = Invoke-RestMethod -Uri $URL -Method POST -Headers $headers -Body $body -TimeoutSec 30
        Write-Host "✅ Key #${keyNum} 成功!" -ForegroundColor Green
        Write-Host "回复: $($response.choices[0].message.content)" -ForegroundColor Cyan
    }
    catch {
        Write-Host "❌ Key #${keyNum} 失败!" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "状态码: $statusCode" -ForegroundColor Red
        }
    }
    
    Write-Host "---"
    Start-Sleep -Seconds 1
}

Write-Host "测试完成!" -ForegroundColor Blue
